{"samples_save": true, "samples_format": "png", "samples_filename_pattern": "", "save_images_add_number": true, "grid_save": true, "grid_format": "png", "grid_extended_filename": false, "grid_only_if_multiple": true, "grid_prevent_empty_spots": false, "n_rows": -1, "enable_pnginfo": true, "save_txt": false, "save_images_before_face_restoration": false, "save_images_before_highres_fix": false, "save_images_before_color_correction": false, "jpeg_quality": 80, "export_for_4chan": true, "use_original_name_batch": true, "use_upscaler_name_as_suffix": true, "save_selected_only": true, "do_not_add_watermark": false, "temp_dir": "", "clean_temp_dir_at_start": false, "outdir_samples": "", "outdir_txt2img_samples": "outputs/txt2img-images", "outdir_img2img_samples": "outputs/img2img-images", "outdir_extras_samples": "outputs/extras-images", "outdir_grids": "", "outdir_txt2img_grids": "outputs/txt2img-grids", "outdir_img2img_grids": "outputs/img2img-grids", "outdir_save": "log/images", "save_to_dirs": false, "grid_save_to_dirs": false, "use_save_to_dirs_for_ui": false, "directories_filename_pattern": "[date]", "directories_max_prompt_words": 8, "ESRGAN_tile": 192, "ESRGAN_tile_overlap": 8, "realesrgan_enabled_models": ["R-ESRGAN 4x+", "R-ESRGAN 4x+ Anime6B", "R-ESRGAN General 4xV3", "R-ESRGAN General WDN 4xV3", "R-ESRGAN AnimeVideo"], "upscaler_for_img2img": "R-ESRGAN 4x+", "face_restoration_model": "GFPGAN", "code_former_weight": 0.5, "face_restoration_unload": false, "show_warnings": false, "memmon_poll_rate": 8, "samples_log_stdout": false, "multiple_tqdm": true, "print_hypernet_extra": false, "unload_models_when_training": true, "pin_memory": false, "save_optimizer_state": false, "save_training_settings_to_txt": true, "dataset_filename_word_regex": "", "dataset_filename_join_string": " ", "training_image_repeats_per_epoch": 1, "training_write_csv_every": 500, "training_xattention_optimizations": false, "training_enable_tensorboard": false, "training_tensorboard_save_images": false, "training_tensorboard_flush_every": 120, "sd_model_checkpoint": "Photo style 照片风格 CNvtuberMix_v1.0.ckpt [509404c4e7]", "sd_checkpoint_cache": 0, "sd_vae_checkpoint_cache": 0, "sd_vae": "Automatic", "sd_vae_as_default": false, "inpainting_mask_weight": 1.0, "initial_noise_multiplier": 1.0, "img2img_color_correction": false, "img2img_fix_steps": false, "img2img_background_color": "#ffffff", "enable_quantization": false, "enable_emphasis": true, "enable_batch_seeds": true, "comma_padding_backtrack": 20, "CLIP_stop_at_last_layers": 2, "upcast_attn": false, "use_old_emphasis_implementation": false, "use_old_karras_scheduler_sigmas": false, "use_old_hires_fix_width_height": false, "interrogate_keep_models_in_memory": false, "interrogate_return_ranks": false, "interrogate_clip_num_beams": 1, "interrogate_clip_min_length": 24, "interrogate_clip_max_length": 48, "interrogate_clip_dict_limit": 1500, "interrogate_clip_skip_categories": [], "interrogate_deepbooru_score_threshold": 0.7, "deepbooru_sort_alpha": false, "deepbooru_use_spaces": false, "deepbooru_escape": true, "deepbooru_filter_tags": "", "extra_networks_default_view": "cards", "extra_networks_default_multiplier": 1.0, "sd_hypernetwork": "None", "return_grid": true, "do_not_show_images": false, "add_model_hash_to_info": true, "add_model_name_to_info": true, "disable_weights_auto_swap": true, "send_seed": true, "send_size": true, "font": "", "js_modal_lightbox": true, "js_modal_lightbox_initially_zoomed": true, "show_progress_in_title": true, "samplers_in_dropdown": false, "dimensions_and_batch_together": true, "keyedit_precision_attention": 0.1, "keyedit_precision_extra": 0.05, "quicksettings": "sd_model_checkpoint, sd_vae, CLIP_stop_at_last_layers", "ui_reorder": "inpaint, sampler, checkboxes, hires_fix, dimensions, cfg, seed, batch, override_settings, scripts", "ui_extra_networks_tab_reorder": "", "localization": "None", "show_progressbar": true, "live_previews_enable": false, "show_progress_grid": true, "show_progress_every_n_steps": 10, "show_progress_type": "TAESD", "live_preview_content": "Prompt", "live_preview_refresh_period": 1000, "hide_samplers": [], "eta_ddim": 0.0, "eta_ancestral": 1.0, "ddim_discretize": "uniform", "s_churn": 0.0, "s_tmin": 0.0, "s_noise": 1.0, "eta_noise_seed_delta": 31337, "always_discard_next_to_last_sigma": false, "postprocessing_enable_in_main_ui": [], "postprocessing_operation_order": [], "upscaling_max_images_in_cache": 5, "disabled_extensions": [], "sd_checkpoint_hash": "509404c4e79d5b7cdfe1355c1fcd88be3400c9288ef84294f2c74930864ce28a", "ldsr_steps": 100, "ldsr_cached": false, "SWIN_tile": 192, "SWIN_tile_overlap": 8, "sd_lora": "None", "lora_apply_to_outputs": false, "tac_tagFile": "danbooru.csv", "tac_active": true, "tac_activeIn.txt2img": true, "tac_activeIn.img2img": true, "tac_activeIn.negativePrompts": true, "tac_activeIn.thirdParty": true, "tac_activeIn.modelList": "", "tac_activeIn.modelListMode": "Blacklist", "tac_maxResults": 15.0, "tac_showAllResults": false, "tac_resultStepLength": 100.0, "tac_delayTime": 100.0, "tac_useWildcards": true, "tac_useEmbeddings": true, "tac_useHypernetworks": true, "tac_useLoras": true, "tac_showWikiLinks": true, "tac_replaceUnderscores": true, "tac_escapeParentheses": true, "tac_appendComma": true, "tac_alias.searchByAlias": true, "tac_alias.onlyShowAlias": false, "tac_translation.translationFile": "CN.csv", "tac_translation.oldFormat": true, "tac_translation.searchByTranslation": true, "tac_extra.extraFile": "extra-quality-tags.csv", "tac_extra.addMode": "Insert before", "additional_networks_extra_lora_path": "", "additional_networks_sort_models_by": "name", "additional_networks_reverse_sort_order": false, "additional_networks_model_name_filter": "", "additional_networks_xy_grid_model_metadata": "", "additional_networks_hash_thread_count": 1.0, "additional_networks_back_up_model_when_saving": true, "additional_networks_show_only_safetensors": false, "additional_networks_show_only_models_with_metadata": "disabled", "additional_networks_max_top_tags": 20.0, "additional_networks_max_dataset_folders": 20.0, "images_record_paths": true, "img_downscale_threshold": 4.0, "target_side_length": 4000.0, "no_dpmpp_sde_batch_determinism": false, "webp_lossless": false, "img_max_size_mp": 200.0, "extra_networks_add_text_separator": " ", "hidden_tabs": [], "uni_pc_variant": "bh1", "uni_pc_skip_type": "time_uniform", "uni_pc_order": 3, "uni_pc_lower_order_final": true, "tac_slidingPopup": true, "tac_keymap": "{\n    \"MoveUp\": \"ArrowUp\",\n    \"MoveDown\": \"ArrowDown\",\n    \"JumpUp\": \"PageUp\",\n    \"JumpDown\": \"PageDown\",\n    \"JumpToStart\": \"Home\",\n    \"JumpToEnd\": \"End\",\n    \"ChooseSelected\": \"Enter\",\n    \"ChooseFirstOrSelected\": \"Tab\",\n    \"Close\": \"Escape\"\n}", "tac_colormap": "{\n    \"danbooru\": {\n        \"-1\": [\"red\", \"maroon\"],\n        \"0\": [\"lightblue\", \"dodgerblue\"],\n        \"1\": [\"indianred\", \"firebrick\"],\n        \"3\": [\"violet\", \"darkorchid\"],\n        \"4\": [\"lightgreen\", \"darkgreen\"],\n        \"5\": [\"orange\", \"darkorange\"]\n    },\n    \"e621\": {\n        \"-1\": [\"red\", \"maroon\"],\n        \"0\": [\"lightblue\", \"dodgerblue\"],\n        \"1\": [\"gold\", \"goldenrod\"],\n        \"3\": [\"violet\", \"darkorchid\"],\n        \"4\": [\"lightgreen\", \"darkgreen\"],\n        \"5\": [\"tomato\", \"darksalmon\"],\n        \"6\": [\"red\", \"maroon\"],\n        \"7\": [\"whitesmoke\", \"black\"],\n        \"8\": [\"seagreen\", \"darkseagreen\"]\n    }\n}", "control_net_model_config": "models\\cldm_v21.yaml", "control_net_model_adapter_config": "models\\sketch_adapter_v14.yaml", "control_net_detectedmap_dir": "detected_maps", "control_net_models_path": "", "control_net_max_models_num": 5, "control_net_model_cache_size": 1, "control_net_control_transfer": false, "control_net_no_detectmap": true, "control_net_detectmap_autosaving": false, "control_net_only_midctrl_hires": true, "control_net_allow_script_control": true, "control_net_skip_img2img_processing": false, "control_net_monocular_depth_optim": false, "control_net_only_mid_control": false, "control_net_cfg_based_guidance": false, "control_net_sync_field_args": true, "image_browser_delete_message": true, "image_browser_page_columns": 6.0, "image_browser_page_rows": 6.0, "image_browser_pages_perload": 20.0, "restore_config_state_file": "", "image_browser_active_tabs": "txt2img, img2img, txt2img-grids, img2img-grids, Extras, Favorites, Others, All, Maintenance", "disable_all_extensions": "none", "save_mask": false, "save_mask_composite": false, "save_init_img": false, "outdir_init_images": "outputs/init-images", "SCUNET_tile": 256, "SCUNET_tile_overlap": 8, "randn_source": "GPU", "dont_fix_second_order_samplers_schedule": false, "extra_networks_card_width": 0.0, "extra_networks_card_height": 0.0, "return_mask": false, "return_mask_composite": false, "keyedit_delimiters": ".,\\/!?%^*;:{}=`~()", "gradio_theme": "<PERSON><PERSON><PERSON>", "s_min_uncond": 0, "ad_max_models": 2, "ad_save_previews": false, "ad_save_images_before": false, "ad_only_seleted_scripts": true, "ad_script_names": "dynamic_prompting,dynamic_thresholding,wildcards,wildcard_recursive", "image_browser_hidden_components": [], "image_browser_with_subdirs": true, "image_browser_copy_image": false, "image_browser_txt_files": true, "image_browser_debug_level": "0 - none", "image_browser_delete_recycle": false, "image_browser_scan_exif": true, "image_browser_mod_shift": false, "image_browser_mod_ctrl_shift": false, "image_browser_enable_maint": true, "image_browser_ranking_pnginfo": false, "image_browser_use_thumbnail": false, "image_browser_thumbnail_size": 200.0, "image_browser_swipe": false, "tac_useLycos": true, "tac_translation.liveTranslation": false, "tac_chantFile": "demo-chants.json", "infzoom_outpath": "outputs", "infzoom_outSUBpath": "infinite-zooms", "infzoom_outsizeW": 512, "infzoom_outsizeH": 512, "infzoom_ffprobepath": "", "infzoom_defPrompt": "{\n        \"prePrompt\": \"Huge spectacular Waterfall in \",\n        \"prompts\": {\n                \"data\": [\n                        [0, \"a dense tropical forest\"],\n                        [2, \"a Lush jungle\"],\n                        [3, \"a Thick rainforest\"],\n                        [5, \"a Verdant canopy\"]\n                ]\n        },\n        \"postPrompt\": \"epic perspective,(vegetation overgrowth:1.3)(intricate, ornamentation:1.1),(baroque:1.1), fantasy, (realistic:1) digital painting , (magical,mystical:1.2) , (wide angle shot:1.4), (landscape composed:1.2)(medieval:1.1),(tropical forest:1.4),(river:1.3) volumetric lighting ,epic, style by <PERSON> gre<PERSON> r<PERSON> (<PERSON>:1.2)\",\n        \"negPrompt\": \"frames, border, edges, borderline, text, character, duplicate, error, out of frame, watermark, low quality, ugly, deformed, blur, bad-artist\"\n}", "infzoom_collectAllResources": false, "openpose3d_use_online_version": false, "bilingual_localization_enabled": true, "bilingual_localization_logger": false, "bilingual_localization_file": "zh_CN", "bilingual_localization_order": "Translation First", "deforum_keep_3d_models_in_vram": false, "deforum_enable_persistent_settings": false, "deforum_persistent_settings_path": "models/Deforum/deforum_persistent_settings.txt", "deforum_ffmpeg_location": "F:\\Program Files\\sd-webui-aki-v4\\py310\\lib\\site-packages\\imageio_ffmpeg\\binaries\\ffmpeg-win64-v4.2.2.exe", "deforum_ffmpeg_crf": 17, "deforum_ffmpeg_preset": "slow", "deforum_debug_mode_enabled": false, "deforum_save_gen_info_as_srt": false, "deforum_embed_srt": false, "deforum_save_gen_info_as_srt_params": ["Noise Schedule"], "image_browser_preload": false, "ad_bbox_sortby": "None", "control_net_modules_path": "", "controlnet_show_batch_images_in_ui": true, "controlnet_increment_seed_during_batch": false, "image_browser_img_tooltips": true, "image_browser_scoring_type": "aesthetic_score", "image_browser_show_progress": true, "mov2mov_outpath_samples": "outputs/mov2mov-images", "mov2mov_output_dir": "outputs/mov2mov-videos", "sd_lyco": "None", "quicksettings_list": ["sd_model_checkpoint", "sd_vae", "CLIP_stop_at_last_layers", "cross_attention_optimization", "token_merging_ratio", "token_merging_ratio_img2img", "token_merging_ratio_hr", "show_progress_type"], "list_hidden_files": true, "cross_attention_optimization": "Automatic", "token_merging_ratio": 0, "token_merging_ratio_img2img": 0, "token_merging_ratio_hr": 0, "lora_functional": false, "extra_networks_show_hidden_directories": true, "extra_networks_hidden_models": "When searched", "lora_preferred_name": "<PERSON><PERSON> from file", "lora_add_hashes_to_infotext": true, "img2img_editor_height": 720, "js_modal_lightbox_gamepad": false, "js_modal_lightbox_gamepad_repeat": 250.0, "ui_tab_order": [], "hires_fix_show_sampler": true, "hires_fix_show_prompts": true, "add_version_to_infotext": true, "live_previews_image_format": "png", "sadtalker_result_dir": "F:\\Program Files\\sd-webui-aki-v4\\outputs\\SadTalker", "modelscope_deforum_keep_model_in_vram": "None", "modelscope_deforum_vae_settings": "GPU (half precision)", "modelscope_deforum_show_n_videos": -1.0, "tac_refreshTempFiles": "Refresh TAC temp files", "controlnet_disable_control_type": false, "controlnet_disable_openpose_edit": false, "sam_use_local_groundingdino": false, "image_browser_info_add": false, "ui_reorder_list": ["inpaint", "sampler", "checkboxes", "hires_fix", "dimensions", "cfg", "seed", "batch", "override_settings", "scripts"], "sd_vae_overrides_per_model_preferences": true, "SWIN_torch_compile": false, "hypertile_enable_unet": false, "hypertile_enable_unet_secondpass": false, "hypertile_max_depth_unet": 3, "hypertile_max_tile_unet": 256, "hypertile_swap_size_unet": 3, "hypertile_enable_vae": false, "hypertile_max_depth_vae": 3, "hypertile_max_tile_vae": 128, "hypertile_swap_size_vae": 3, "ch_max_size_preview": true, "ch_skip_nsfw_preview": false, "ch_open_url_with_js": true, "ch_proxy": "", "ch_civiai_api_key": "", "ad_extra_models_dir": "", "ad_same_seed_for_each_tap": false, "deforum_preview": "Off", "deforum_preview_interval_frames": 100, "infzoom_txt2img_model": null, "infzoom_inpainting_model": null, "face_editor_search_subdirectories": false, "face_editor_additional_components": [], "face_editor_save_original_on_detection_fail": true, "face_editor_correct_tilt": false, "face_editor_auto_face_size_by_model": false, "face_editor_script_index": 99, "bilingual_localization_dirs": "{\"zh_CN\": \"extensions/stable-diffusion-webui-localization-zh_CN/localizations/zh_CN.json\"}", "comfyui_enabled": true, "comfyui_update_button": "Update comfyui (requires reload ui)", "comfyui_install_location": "F:\\Program Files\\sd-webui-aki-v4\\extensions\\sd-webui-comfyui\\ComfyUI", "comfyui_additional_args": "", "comfyui_client_address": "", "comfyui_ipc_strategy": "<PERSON><PERSON><PERSON>", "comfyui_graceful_termination_timeout": 5, "comfyui_reverse_proxy_enabled": "<PERSON><PERSON><PERSON>", "comfyui_reverse_proxy_disable_port": false, "control_net_unit_count": 3, "control_net_inpaint_blur_sigma": 7, "controlnet_disable_photopea_edit": false, "controlnet_photopea_warning": true, "controlnet_ignore_noninpaint_mask": false, "controlnet_clip_detector_on_cpu": false, "controlnet_control_type_dropdown": false, "modelscope_save_info_to_file": false, "modelscope_save_metadata": true, "image_browser_height_auto": false, "image_browser_thumbnail_crop": false, "image_browser_video_pos": "Above", "image_browser_video_x": 640, "image_browser_video_y": 640, "tac_sortWildcardResults": true, "tac_wildcardExclusionList": "", "tac_skipWildcardRefresh": false, "tac_includeEmbeddingsInNormalResults": false, "tac_useLoraPrefixForLycos": true, "tac_showExtraNetworkPreviews": true, "tac_modelSortOrder": "Name", "tac_useStyleVars": false, "tac_frequencySort": true, "tac_frequencyFunction": "Logar<PERSON><PERSON> (weak)", "tac_frequencyMinCount": 3, "tac_frequencyMaxAge": 30, "tac_frequencyRecommendCap": 10, "tac_frequencyIncludeAlias": false, "tac_appendSpace": true, "tac_alwaysSpaceAtEnd": true, "tac_modelKeywordCompletion": "Never", "tac_modelKeywordLocation": "Start of prompt", "tac_wildcardCompletionMode": "To next folder level", "animatediff_model_path": null, "animatediff_default_save_formats": ["GIF", "PNG"], "animatediff_save_to_custom": true, "animatediff_frame_extract_path": null, "animatediff_frame_extract_remove": false, "animatediff_default_frame_extract_method": "ffmpeg", "animatediff_optimize_gif_palette": false, "animatediff_optimize_gif_gifsicle": false, "animatediff_mp4_crf": 23, "animatediff_mp4_preset": "", "animatediff_mp4_tune": "", "animatediff_webp_quality": 80, "animatediff_webp_lossless": false, "animatediff_s3_enable": false, "animatediff_s3_host": null, "animatediff_s3_port": null, "animatediff_s3_access_key": null, "animatediff_s3_secret_key": null, "animatediff_s3_storge_bucket": null}