model:
  base_learning_rate: 4.5e-06
  target: taming.models.vqgan.VQSegmentationModel
  params:
    embed_dim: 256
    n_embed: 1024
    image_key: "segmentation"
    n_labels: 182
    ddconfig:
      double_z: false
      z_channels: 256
      resolution: 256
      in_channels: 182
      out_ch: 182
      ch: 128
      ch_mult:
      - 1
      - 1
      - 2
      - 2
      - 4
      num_res_blocks: 2
      attn_resolutions:
      - 16
      dropout: 0.0

    lossconfig:
      target: taming.modules.losses.segmentation.BCELossWithQuant
      params:
        codebook_weight: 1.0

data:
  target: cutlit.DataModuleFromConfig
  params:
    batch_size: 12
    train:
      target: taming.data.sflckr.Examples # adjust
      params:
        size: 256
    validation:
      target: taming.data.sflckr.Examples # adjust
      params:
        size: 256
