model:
  base_learning_rate: 4.5e-06
  target: taming.models.vqgan.VQSegmentationModel
  params:
    embed_dim: 256
    n_embed: 1024
    image_key: "segmentation"
    n_labels: 183
    ddconfig:
      double_z: false
      z_channels: 256
      resolution: 256
      in_channels: 183
      out_ch: 183
      ch: 128
      ch_mult:
      - 1
      - 1
      - 2
      - 2
      - 4
      num_res_blocks: 2
      attn_resolutions:
      - 16
      dropout: 0.0

    lossconfig:
      target: taming.modules.losses.segmentation.BCELossWithQuant
      params:
        codebook_weight: 1.0

data:
  target: main.DataModuleFromConfig
  params:
    batch_size: 12
    train:
      target: taming.data.coco.CocoImagesAndCaptionsTrain
      params:
        size: 296
        crop_size: 256
        onehot_segmentation: true
        use_stuffthing: true
    validation:
      target: taming.data.coco.CocoImagesAndCaptionsValidation
      params:
        size: 256
        crop_size: 256
        onehot_segmentation: true
        use_stuffthing: true
