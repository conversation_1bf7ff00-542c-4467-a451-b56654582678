Looking in indexes: https://mirrors.bfsu.edu.cn/pypi/web/simple, https://pypi.tuna.tsinghua.edu.cn/simple, https://pypi.doubanio.com/simple
Looking in links: https://mirror.sjtu.edu.cn/pytorch-wheels/torch_stable.html
Requirement already satisfied: scikit-image in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (0.20.0)
Requirement already satisfied: numpy>=1.21.1 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (1.23.5)
Requirement already satisfied: scipy>=1.8 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (1.10.1)
Requirement already satisfied: networkx>=2.8 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (3.1)
Requirement already satisfied: pillow>=9.0.1 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (9.5.0)
Requirement already satisfied: imageio>=2.4.1 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (2.28.1)
Requirement already satisfied: tifffile>=2019.7.26 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (2023.4.12)
Requirement already satisfied: PyWavelets>=1.1.1 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (1.4.1)
Requirement already satisfied: packaging>=20.0 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (23.1)
Requirement already satisfied: lazy_loader>=0.1 in e:\programdata\anaconda3\envs\stable-diffusion-webui\lib\site-packages (from scikit-image) (0.2)
